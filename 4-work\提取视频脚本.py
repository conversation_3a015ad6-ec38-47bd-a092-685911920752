import pandas as pd
import json
import re
from typing import Dict, Any

def robust_json_parse(json_text: str) -> Dict[str, str]:
    """
    强健的JSON解析器，尝试多种解析策略，提取title和content
    """
    if pd.isna(json_text) or not json_text:
        return {'title': '', 'content': ''}
    
    json_text = str(json_text).strip()
    
    # 策略1: 标准双层JSON解析
    try:
        outer_json = json.loads(json_text)
        text_content = outer_json.get('text', '')
        inner_data = json.loads(text_content)
        
        if isinstance(inner_data, list) and len(inner_data) > 0:
            item = inner_data[0]
        else:
            item = inner_data
        
        title = item.get('title', '')
        # 清理标题中的《》
        title = re.sub(r'《([^》]+)》', r'\1', title)
        title = title.strip()
        
        # 直接从根级别获取content字段
        content = item.get('content', '')
        # 清理内容中的**
        content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)
        # 清理内容，修复空格问题
        final_content = clean_content_spaces(content).strip()
        
        if title or final_content:
            print(f"    策略1成功: JSON标准解析")
            return {'title': title, 'content': final_content}
            
    except Exception as e:
        pass
    
    # 策略2: 处理转义问题的JSON解析
    try:
        # 首先移除外层包装
        if json_text.startswith('{"text":"') and json_text.endswith('"}'):
            inner_json = json_text[9:-2]
            
            # 处理各种转义字符问题
            inner_json = inner_json.replace('\\"', '"')
            inner_json = inner_json.replace('\\\\', '\\')
            
            # 尝试解析
            data = json.loads(inner_json)
            
            if isinstance(data, list) and len(data) > 0:
                item = data[0]
            else:
                item = data
            
            title = item.get('title', '')
            title = re.sub(r'《([^》]+)》', r'\1', title)
            title = title.strip()
            
            # 直接从根级别获取content字段
            content = item.get('content', '')
            # 清理内容中的**
            content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)
            # 清理内容，修复空格问题
            final_content = clean_content_spaces(content).strip()
            
            if title or final_content:
                print(f"    策略2成功: 转义字符处理")
                return {'title': title, 'content': final_content}
                
    except Exception as e:
        pass
    
    # 策略3: 正则表达式提取title和segment内容
    try:
        # 提取title
        title_match = re.search(r'"title"\s*:\s*"([^"]+)"', json_text)
        if not title_match:
            title_match = re.search(r'"title"\s*:\s*\\+"([^"]+)\\+"', json_text)
            
        title = title_match.group(1) if title_match else ""
        title = re.sub(r'《([^》]+)》', r'\1', title)
        title = title.strip()
        
        # 提取所有content部分
        content_matches = re.findall(r'"content"\s*:\s*"([^"]+)"', json_text)
        if not content_matches:
            content_matches = re.findall(r'"content"\s*:\s*\\+"([^"]+)\\+"', json_text)
        
        content_parts = []
        for content in content_matches:
            content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)
            content = re.sub(r'<[^>]+>', '', content)  # 移除HTML标签
            content = clean_content_spaces(content)
            if content:
                content_parts.append(content)
        
        final_content = ''.join(content_parts).strip()
        
        if title or final_content:
            print(f"    策略3成功: 正则表达式提取")
            return {'title': title, 'content': final_content}
            
    except Exception as e:
        pass
    
    # 策略4: 暴力提取
    try:
        # 1. 先尝试从JSON字符串中提取所有可能的字段
        json_text_clean = json_text.replace('\\"', '"').replace('\\\\', '\\')
        
        # 2. 提取title
        title = ""
        title_match = re.search(r'title\"\s*:\s*\"([^\"]+)\"', json_text_clean)
        if title_match:
            title = title_match.group(1)
            title = re.sub(r'《|》', '', title)  # 移除《》
            title = title.strip()
        
        # 3. 提取content
        content_parts = []
        # 针对segments数组中的content字段进行提取
        content_matches = re.findall(r'content\"\s*:\s*\"([^\"]+)\"', json_text_clean)
        
        for content in content_matches:
            # 过滤掉一些不太可能是正文内容的短文本
            if len(content) > 10 and not content.startswith('{') and not content.startswith('['):
                content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)  # 移除**
                content = re.sub(r'<[^>]+>', '', content)  # 移除HTML标签
                content = clean_content_spaces(content)
                if content:
                    content_parts.append(content)
        
        final_content = ''.join(content_parts).strip()
        
        if title or final_content:
            print(f"    策略4成功: 暴力文本提取")
            return {'title': title, 'content': final_content}
        
    except Exception as e:
        pass
    
    print(f"    所有策略都失败")
    return {'title': '', 'content': ''}

def clean_content_spaces(content: str) -> str:
    """
    清理内容中的空格问题，移除所有空格（适用于中文内容）
    """
    if not content:
        return ""
    
    # 直接移除所有空白字符（空格、换行、制表符等）
    content = re.sub(r'\s+', '', content)
    
    return content

def process_csv_file(input_file: str, output_file: str = None, output_format: str = 'csv'):
    """
    处理CSV文件，将生成结果列拆分为title和content列
    """
    try:
        # 读取CSV文件
        print("正在读取CSV文件...")
        df = pd.read_csv(input_file, encoding='utf-8')
        
        # 检查是否存在"生成结果"列
        if '生成结果' not in df.columns:
            print("错误：CSV文件中没有找到'生成结果'列")
            print(f"现有列名: {list(df.columns)}")
            return
        
        print(f"成功读取{len(df)}行数据")
        
        # 创建新的title和content列
        titles = []
        contents = []
        successful_count = 0
        
        # 处理每一行的生成结果
        for index, row in df.iterrows():
            print(f"\n正在处理第{index + 1}行数据...")
            
            result_data = row['生成结果']
            if pd.isna(result_data):
                titles.append('')
                contents.append('')
                continue
            
            # 显示原始数据的一部分
            print(f"  数据长度: {len(str(result_data))} 字符")
            print(f"  数据开头: {str(result_data)[:100]}...")
            
            # 使用强健解析器
            extracted = robust_json_parse(result_data)
            titles.append(extracted['title'])
            contents.append(extracted['content'])
            
            # 统计成功解析的数量
            if extracted['title'] or extracted['content']:
                successful_count += 1
                print(f"  ✓ 提取成功!")
                print(f"    Title: {extracted['title'][:50]}...")
                print(f"    Content: {extracted['content'][:80]}...")
            else:
                print(f"  ✗ 提取失败")
        
        # 添加新列到DataFrame
        df['title'] = titles
        df['content'] = contents
        
        # 确定输出文件名
        if output_file is None:
            base_name = input_file.replace('.csv', '')
            if output_format == 'xlsx':
                output_file = f"{base_name}_final.xlsx"
            else:
                output_file = f"{base_name}_final.csv"
        
        # 保存处理后的数据
        try:
            if output_format == 'xlsx':
                print(f"\n正在保存为Excel文件: {output_file}")
                df.to_excel(output_file, index=False, engine='openpyxl')
            else:
                print(f"\n正在保存为CSV文件: {output_file}")
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
                
            print(f"✅ 文件保存成功: {output_file}")
            
        except PermissionError:
            # 如果文件被占用，尝试另一个文件名
            import time
            timestamp = int(time.time())
            if output_format == 'xlsx':
                output_file = f"{base_name}_final_{timestamp}.xlsx"
                df.to_excel(output_file, index=False, engine='openpyxl')
            else:
                output_file = f"{base_name}_final_{timestamp}.csv"
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"✅ 文件保存成功（使用时间戳）: {output_file}")
        
        print(f"\n🎉 处理完成！")
        print(f"📊 统计信息:")
        print(f"   - 总行数: {len(df)}")
        print(f"   - 成功解析: {successful_count}行")
        print(f"   - 失败行数: {len(df) - successful_count}行")
        print(f"   - 成功率: {successful_count/len(df)*100:.1f}%")
        
        # 显示处理结果的样例
        print(f"\n📋 前5行成功解析的结果预览:")
        print("=" * 80)
        shown_count = 0
        for i in range(len(df)):
            if (df.loc[i, 'title'] or df.loc[i, 'content']) and shown_count < 5:
                print(f"第{i+1}行:")
                print(f"Title: {df.loc[i, 'title']}")
                print(f"Content: {df.loc[i, 'content'][:150]}...")
                print("-" * 40)
                shown_count += 1
        
        if shown_count == 0:
            print("没有成功解析的数据可以预览")
            
    except FileNotFoundError:
        print(f"❌ 错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """
    主函数
    """
    print("🚀 终极CSV解析器 - 修复空格版")
    print("=" * 60)
    print("支持4种不同的解析策略，并修复了中文空格问题")
    
    # 设置输入文件路径
    input_file = "E:/result.csv"
    
    # 检查文件是否存在
    import os
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到文件 {input_file}")
        print(f"当前目录: {os.getcwd()}")
        return
    
    # 处理文件
    output_format = 'xlsx' 
    process_csv_file(input_file, output_format=output_format)

if __name__ == "__main__":
    main()