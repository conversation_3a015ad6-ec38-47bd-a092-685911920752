2025-07-31 15:32:53,466 - ERROR - ❌ 源数据文件不存在: e:\python\4-work\品牌信息.csv
2025-07-31 15:33:52,486 - INFO - 🚀 开始执行模板自动填充...
2025-07-31 15:33:52,486 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-07-31 15:33:52,486 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-07-31 15:33:52,490 - INFO - 📋 模板表头加载成功: 5个字段
2025-07-31 15:33:52,490 - INFO - 表头: ['店铺名称', '主营业务', '店铺亮点', '行业', '产出数量']
2025-07-31 15:33:52,553 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-07-31 15:33:52,553 - INFO - 📊 源数据文件编码: GB2312
2025-07-31 15:33:52,558 - INFO - 📊 源数据加载成功: 38行 x 36列
2025-07-31 15:33:52,558 - INFO - 源数据表头: ['产出数量', '行业', '主营业务', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '产品/服务名称', '产品/服务明细（例如包含的内容、特点、退款政策等）', '产品特点', '产品/服务优势', '售价', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-07-31 15:33:52,559 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-07-31 15:33:52,559 - INFO - ✅ 表头匹配成功: '主营业务'
2025-07-31 15:33:52,559 - WARNING - ⚠️ 表头未匹配: '店铺亮点'
2025-07-31 15:33:52,559 - INFO - ✅ 表头匹配成功: '行业'
2025-07-31 15:33:52,559 - INFO - ✅ 表头匹配成功: '产出数量'
2025-07-31 15:33:52,560 - INFO - 🎯 匹配结果: 4/5 个表头匹配成功
2025-07-31 15:33:52,560 - WARNING - 未匹配的表头: ['店铺亮点']
2025-07-31 15:33:52,560 - INFO - 💡 提示: 请检查表头名称是否完全一致（包括空格、标点符号）
2025-07-31 15:33:52,561 - INFO - 📤 数据提取成功: 38行 x 4列
2025-07-31 15:33:52,563 - INFO - ✨ 模板填充完成: 38行数据
2025-07-31 15:33:52,565 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-07-31 15:33:52,566 - INFO - 📊 输出数据: 38行 x 5列
2025-07-31 15:33:52,566 - INFO - 🎉 模板填充完成！
2025-07-31 15:35:34,407 - INFO - 🚀 开始执行模板自动填充...
2025-07-31 15:35:34,411 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-07-31 15:35:34,412 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-07-31 15:35:34,422 - INFO - 📋 模板表头加载成功: 5个字段
2025-07-31 15:35:34,424 - INFO - 表头: ['店铺名称', '主营业务', '店铺亮点', '行业', '产出数量']
2025-07-31 15:35:34,491 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-07-31 15:35:34,495 - INFO - 📊 源数据文件编码: GB2312
2025-07-31 15:35:34,503 - INFO - 📊 源数据加载成功: 38行 x 37列
2025-07-31 15:35:34,506 - INFO - 源数据表头: ['产出数量', '行业', '主营业务', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '产品/服务名称', '产品/服务明细（例如包含的内容、特点、退款政策等）', '产品特点', '产品/服务优势', '售价', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺亮点', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-07-31 15:35:34,507 - INFO - ✅ 表头匹配成功: '店铺名称'
2025-07-31 15:35:34,508 - INFO - ✅ 表头匹配成功: '主营业务'
2025-07-31 15:35:34,509 - INFO - ✅ 表头匹配成功: '店铺亮点'
2025-07-31 15:35:34,510 - INFO - ✅ 表头匹配成功: '行业'
2025-07-31 15:35:34,511 - INFO - ✅ 表头匹配成功: '产出数量'
2025-07-31 15:35:34,515 - INFO - 🎯 匹配结果: 5/5 个表头匹配成功
2025-07-31 15:35:34,517 - INFO - 📤 数据提取成功: 38行 x 5列
2025-07-31 15:35:34,519 - INFO - ✨ 模板填充完成: 38行数据
2025-07-31 15:35:34,525 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-07-31 15:35:34,525 - INFO - 📊 输出数据: 38行 x 5列
2025-07-31 15:35:34,526 - INFO - 🎉 模板填充完成！
2025-07-31 15:54:57,815 - INFO - 🚀 开始执行模板自动填充...
2025-07-31 15:54:57,816 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-07-31 15:54:57,816 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-07-31 15:54:57,820 - INFO - 📋 模板表头加载成功: 6个字段
2025-07-31 15:54:57,820 - INFO - 表头: ['经营地点', '服务/产品名称', '脚本数量', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）']
2025-07-31 15:54:57,888 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-07-31 15:54:57,888 - INFO - 📊 源数据文件编码: GB2312
2025-07-31 15:54:57,894 - INFO - 📊 源数据加载成功: 38行 x 37列
2025-07-31 15:54:57,894 - INFO - 源数据表头: ['产出数量', '行业', '主营业务', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '产品/服务名称', '产品/服务明细（例如包含的内容、特点、退款政策等）', '产品特点', '产品/服务优势', '售价', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺亮点', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-07-31 15:54:57,894 - INFO - ✅ 表头匹配成功: '经营地点'
2025-07-31 15:54:57,894 - WARNING - ⚠️ 表头未匹配: '服务/产品名称'
2025-07-31 15:54:57,895 - WARNING - ⚠️ 表头未匹配: '脚本数量'
2025-07-31 15:54:57,895 - WARNING - ⚠️ 表头未匹配: '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）'
2025-07-31 15:54:57,895 - WARNING - ⚠️ 表头未匹配: '关键信息（例如款式多，价格实惠，设备先进，技术专业）'
2025-07-31 15:54:57,895 - WARNING - ⚠️ 表头未匹配: '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）'
2025-07-31 15:54:57,896 - INFO - 🎯 匹配结果: 1/6 个表头匹配成功
2025-07-31 15:54:57,896 - WARNING - 未匹配的表头: ['服务/产品名称', '脚本数量', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）']
2025-07-31 15:54:57,896 - INFO - 💡 提示: 请检查表头名称是否完全一致（包括空格、标点符号）
2025-07-31 15:54:57,898 - INFO - 📤 数据提取成功: 38行 x 1列
2025-07-31 15:54:57,900 - INFO - ✨ 模板填充完成: 38行数据
2025-07-31 15:54:57,902 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-07-31 15:54:57,902 - INFO - 📊 输出数据: 38行 x 6列
2025-07-31 15:54:57,902 - INFO - 🎉 模板填充完成！
2025-07-31 15:58:37,620 - INFO - 🚀 开始执行模板自动填充...
2025-07-31 15:58:37,620 - INFO - 🔍 chardet检测结果: UTF-8-SIG (置信度: 1.00)
2025-07-31 15:58:37,621 - INFO - 📋 模板文件编码: UTF-8-SIG
2025-07-31 15:58:37,625 - INFO - 📋 模板表头加载成功: 6个字段
2025-07-31 15:58:37,625 - INFO - 表头: ['经营地点', '服务/产品名称', '脚本数量', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）']
2025-07-31 15:58:37,700 - INFO - 🔍 chardet检测结果: GB2312 (置信度: 0.99)
2025-07-31 15:58:37,700 - INFO - 📊 源数据文件编码: GB2312
2025-07-31 15:58:37,705 - INFO - 📊 源数据加载成功: 38行 x 42列
2025-07-31 15:58:37,705 - INFO - 源数据表头: ['产出数量', '脚本数量', '行业', '主营业务', '品牌名称', '城市名称', '城市优点', '城市槽点', '居住年限', '本地优势（可选）', '行业专有名词（例如面料、材质、工艺等）', '经营地点', '开业时间', '店铺名称', '店铺特色', '投资金额', '人设标签（例如90后/富二代/博士毕业/东北人等）', '老板昵称', '老板性别', '职业身份', '经营理念', '新品名称', '服务/产品名称', '产品/服务名称', '产品/服务明细（例如包含的内容、特点、退款政策等）', '产品特点', '关键信息（例如款式多，价格实惠，设备先进，技术专业）', '产品/服务优势', '售价', '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）', '目标人群', '客户需求（例如有法律纠纷/找门窗/找装修/找团建项目）', '客户顾虑', '关键痛点', '内幕（例如原材料、标价门道、经营秘诀、行业秘密等）', '店铺亮点', '店铺成果（例如天天爆满，回头客多，外地顾客远道而来，好评榜第一）', '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）', '呼吁行动（私信咨询、评论区提问）', '热点话题', '影响（负面影响例如成本上升、口碑破坏；正面影响例如消费者获益、推出全新套餐、优化新设备）', '变化（例如行业趋势、市场趋势、消费者习惯、顾客认知等）']
2025-07-31 15:58:37,706 - INFO - ✅ 表头匹配成功: '经营地点'
2025-07-31 15:58:37,706 - INFO - ✅ 表头匹配成功: '服务/产品名称'
2025-07-31 15:58:37,707 - INFO - ✅ 表头匹配成功: '脚本数量'
2025-07-31 15:58:37,707 - INFO - ✅ 表头匹配成功: '目标人群（上班族/90后/00后/宝妈/奶爸/二次元）'
2025-07-31 15:58:37,707 - INFO - ✅ 表头匹配成功: '关键信息（例如款式多，价格实惠，设备先进，技术专业）'
2025-07-31 15:58:37,707 - INFO - ✅ 表头匹配成功: '呼吁行动（例如评论区留言、点赞收藏、私信咨询、到店体验、到店选购）'
2025-07-31 15:58:37,707 - INFO - 🎯 匹配结果: 6/6 个表头匹配成功
2025-07-31 15:58:37,710 - INFO - 📤 数据提取成功: 38行 x 6列
2025-07-31 15:58:37,713 - INFO - ✨ 模板填充完成: 38行数据
2025-07-31 15:58:37,716 - INFO - 💾 结果保存成功: E:\template_filled.csv
2025-07-31 15:58:37,716 - INFO - 📊 输出数据: 38行 x 6列
2025-07-31 15:58:37,716 - INFO - 🎉 模板填充完成！
